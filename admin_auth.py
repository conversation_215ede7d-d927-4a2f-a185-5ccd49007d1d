import customtkinter as ctk
from database import DatabaseManager

class AdminAuthDialog(ctk.CTkToplevel):
    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__()
        
        self.db_manager = db_manager
        self.parent = parent
        self.authenticated = False
        
        self.setup_window()
        self.create_widgets()
    
    def setup_window(self):
        """Setup the authentication window"""
        self.title("Administrator Authentication")
        self.geometry("400x300")
        self.resizable(False, False)

        # Set custom icon
        try:
            self.iconbitmap("appicon.ico")
        except Exception:
            pass  # Fallback to default icon if file not found

        if self.parent:
            self.transient(self.parent)
        self.grab_set()

        # Center the window
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.winfo_screenheight() // 2) - (300 // 2)
        self.geometry(f"400x300+{x}+{y}")
    
    def create_widgets(self):
        """Create the authentication widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="Administrator Access",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # Description
        desc_label = ctk.CTkLabel(
            main_frame,
            text="Enter administrator password to access the management panel.",
            font=ctk.CTkFont(size=14),
            wraplength=350
        )
        desc_label.pack(pady=(0, 30))
        
        # Password entry
        password_frame = ctk.CTkFrame(main_frame)
        password_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        password_label = ctk.CTkLabel(
            password_frame,
            text="Password:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        password_label.pack(anchor="w", padx=10, pady=(15, 5))
        
        self.password_entry = ctk.CTkEntry(
            password_frame,
            placeholder_text="Enter administrator password",
            show="*",
            font=ctk.CTkFont(size=14)
        )
        self.password_entry.pack(fill="x", padx=10, pady=(0, 15))
        self.password_entry.focus()
        
        # Error message
        self.error_label = ctk.CTkLabel(
            main_frame,
            text="",
            text_color="red",
            font=ctk.CTkFont(size=12)
        )
        self.error_label.pack(pady=(0, 20))
        
        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        cancel_button = ctk.CTkButton(
            button_frame,
            text="Cancel",
            command=self.cancel_auth,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=(0, 10), expand=True)
        
        login_button = ctk.CTkButton(
            button_frame,
            text="Login",
            command=self.authenticate
        )
        login_button.pack(side="right", expand=True)
        
        # Bind Enter key
        self.password_entry.bind('<Return>', lambda e: self.authenticate())
    
    def authenticate(self):
        """Authenticate the administrator"""
        password = self.password_entry.get()
        
        if not password:
            self.error_label.configure(text="Please enter a password.")
            return
        
        if self.db_manager.verify_admin_password(password):
            self.authenticated = True
            self.destroy()
        else:
            self.error_label.configure(text="Invalid password. Please try again.")
            self.password_entry.delete(0, 'end')
    
    def cancel_auth(self):
        """Cancel authentication"""
        self.authenticated = False
        self.destroy()
    
    def is_authenticated(self) -> bool:
        """Check if authentication was successful"""
        return self.authenticated 