# PolicyTime - Employee Time Tracking Application

A modern desktop application for employee sign-in/sign-out tracking with administrator management capabilities.

## Features

- **Employee Interface**: Clean two-panel design with active employee list and status display
- **PIN Security**: Optional 4-digit PIN authentication for employees
- **Administrator Panel**: Password-protected management interface
- **Offline Support**: Graceful handling of network outages with local time fallback
- **Automatic Resolution**: Smart handling of forgotten sign-outs
- **Time Audit**: Background verification of offline entries when connection is restored

## Installation

1. Ensure Python 3.8+ is installed on your system
2. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Initial Setup

1. Run the application for the first time:

   ```bash
   python main.py
   ```

2. The application will prompt you to set up the initial administrator password
3. Enter a secure password (minimum 8 characters)
4. The system will create the database and default configuration

## Usage

### Employee Interface

- Select your name from the left panel
- Enter your 4-digit PIN if enabled
- Confirm sign-in/sign-out action
- View current status and network connectivity

### Administrator Access

- Click the "Admin Panel" button
- Enter the administrator password
- Manage employees, system configuration, and view time logs

## Database

The application uses SQLite (`policytime.db`) stored in the application directory. All data is automatically saved and the application can be safely closed and reopened without data loss.

## Security

- All passwords and PINs are securely hashed using bcrypt
- PIN security can be enabled/disabled globally
- Brute-force protection for PIN entry
- Secure administrator authentication

## Network Requirements

- Internet connection for NTP time verification
- Application works offline with local time fallback
- Automatic audit of offline entries when connection is restored
