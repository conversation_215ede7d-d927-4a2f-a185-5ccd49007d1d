import customtkinter as ctk
from typing import Dict, Any, Optional
from database import DatabaseManager
from time_service import TimeService
import datetime

class AdminPanel(ctk.CTkToplevel):
    def __init__(self, db_manager: DatabaseManager, time_service: TimeService, refresh_callback=None):
        super().__init__()
        
        self.db_manager = db_manager
        self.time_service = time_service
        self.current_employee = None
        self.refresh_callback = refresh_callback  # Callback to refresh main interface
        
        self.setup_window()
        self.create_widgets()
        self.refresh_data()
    
    def setup_window(self):
        """Setup the admin window"""
        self.title("PolicyTime - Administrator Panel")
        self.geometry("1000x700")
        self.resizable(True, True)

        # Set custom icon
        try:
            self.iconbitmap("appicon.ico")
        except Exception:
            pass  # Fallback to default icon if file not found

        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)
    
    def create_widgets(self):
        """Create the admin interface widgets"""
        # Header
        header_frame = ctk.CTkFrame(self)
        header_frame.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="ew")
        
        header_label = ctk.CTkLabel(
            header_frame,
            text="Administrator Panel",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        header_label.pack(pady=20)
        
        # Tab view for different sections
        self.tab_view = ctk.CTkTabview(self)
        self.tab_view.grid(row=1, column=0, padx=10, pady=(5, 10), sticky="nsew")
        
        # Employee Management Tab
        self.employee_tab = self.tab_view.add("Employee Management")
        self.create_employee_management_tab()
        
        # System Configuration Tab
        self.config_tab = self.tab_view.add("System Configuration")
        self.create_system_config_tab()
        
        # Time Logs Tab
        self.logs_tab = self.tab_view.add("Time Logs")
        self.create_time_logs_tab()
    
    def create_employee_management_tab(self):
        """Create the employee management interface"""
        # Left side - Employee list
        left_frame = ctk.CTkFrame(self.employee_tab)
        left_frame.pack(side="left", fill="both", expand=True, padx=(10, 5), pady=10)
        
        # Employee list header
        emp_header = ctk.CTkLabel(
            left_frame,
            text="Active Employees",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        emp_header.pack(pady=(10, 5))
        
        # Employee list
        self.employee_listbox = ctk.CTkScrollableFrame(left_frame)
        self.employee_listbox.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # Right side - Employee details and actions
        right_frame = ctk.CTkFrame(self.employee_tab)
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 10), pady=10)
        
        # Employee details
        details_label = ctk.CTkLabel(
            right_frame,
            text="Employee Details",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        details_label.pack(pady=(10, 20))
        
        # Name entry
        name_frame = ctk.CTkFrame(right_frame)
        name_frame.pack(fill="x", padx=20, pady=(0, 10))
        
        name_label = ctk.CTkLabel(name_frame, text="Name:")
        name_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        self.name_entry = ctk.CTkEntry(name_frame, placeholder_text="Employee name")
        self.name_entry.pack(fill="x", padx=10, pady=(0, 10))
        
        # PIN entry
        pin_frame = ctk.CTkFrame(right_frame)
        pin_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        pin_label = ctk.CTkLabel(pin_frame, text="PIN (4 digits, optional):")
        pin_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        self.pin_entry = ctk.CTkEntry(pin_frame, placeholder_text="Enter 4-digit PIN", show="*")
        self.pin_entry.pack(fill="x", padx=10, pady=(0, 10))
        
        # Action buttons
        button_frame = ctk.CTkFrame(right_frame)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.add_button = ctk.CTkButton(
            button_frame,
            text="Add Employee",
            command=self.add_employee
        )
        self.add_button.pack(fill="x", pady=(10, 5))
        
        self.update_button = ctk.CTkButton(
            button_frame,
            text="Update Employee",
            command=self.update_employee,
            state="disabled"
        )
        self.update_button.pack(fill="x", pady=(0, 5))
        
        self.remove_button = ctk.CTkButton(
            button_frame,
            text="Remove Employee",
            command=self.remove_employee,
            state="disabled",
            fg_color="red"
        )
        self.remove_button.pack(fill="x", pady=(0, 10))
        
        # Clear button
        self.clear_button = ctk.CTkButton(
            button_frame,
            text="Clear Form",
            command=self.clear_employee_form,
            fg_color="gray"
        )
        self.clear_button.pack(fill="x")
    
    def create_system_config_tab(self):
        """Create the system configuration interface"""
        # Main scrollable frame
        main_frame = ctk.CTkScrollableFrame(self.config_tab)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Configuration options
        config_label = ctk.CTkLabel(
            main_frame,
            text="System Configuration",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        config_label.pack(pady=(20, 30))
        
        # PIN requirement toggle
        pin_frame = ctk.CTkFrame(main_frame)
        pin_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.pin_required_var = ctk.BooleanVar()
        self.pin_required_switch = ctk.CTkSwitch(
            pin_frame,
            text="Require PIN for all employees",
            variable=self.pin_required_var,
            command=self.toggle_pin_requirement
        )
        self.pin_required_switch.pack(pady=20)
        
        # Admin panel password toggle
        admin_password_frame = ctk.CTkFrame(main_frame)
        admin_password_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.admin_password_required_var = ctk.BooleanVar()
        self.admin_password_required_switch = ctk.CTkSwitch(
            admin_password_frame,
            text="Require password for admin panel access",
            variable=self.admin_password_required_var,
            command=self.toggle_admin_password_requirement
        )
        self.admin_password_required_switch.pack(pady=20)
        
        # Hard stop time
        hard_stop_frame = ctk.CTkFrame(main_frame)
        hard_stop_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        hard_stop_label = ctk.CTkLabel(
            hard_stop_frame,
            text="Hard Stop Time (for forgotten sign-outs):",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        hard_stop_label.pack(pady=(15, 5))
        
        time_frame = ctk.CTkFrame(hard_stop_frame)
        time_frame.pack(pady=(0, 15))
        
        self.hour_var = ctk.StringVar(value="8")
        self.minute_var = ctk.StringVar(value="00")
        self.ampm_var = ctk.StringVar(value="PM")
        
        hour_label = ctk.CTkLabel(time_frame, text="Hour:")
        hour_label.pack(side="left", padx=(10, 5))
        
        self.hour_entry = ctk.CTkEntry(
            time_frame,
            textvariable=self.hour_var,
            width=60,
            placeholder_text="8"
        )
        self.hour_entry.pack(side="left", padx=(0, 10))
        
        minute_label = ctk.CTkLabel(time_frame, text="Minute:")
        minute_label.pack(side="left", padx=(0, 5))
        
        self.minute_entry = ctk.CTkEntry(
            time_frame,
            textvariable=self.minute_var,
            width=60,
            placeholder_text="00"
        )
        self.minute_entry.pack(side="left", padx=(0, 10))
        
        ampm_label = ctk.CTkLabel(time_frame, text="AM/PM:")
        ampm_label.pack(side="left", padx=(0, 5))
        
        self.ampm_combo = ctk.CTkComboBox(
            time_frame,
            values=["AM", "PM"],
            variable=self.ampm_var,
            width=80
        )
        self.ampm_combo.pack(side="left", padx=(0, 10))
        
        self.save_time_button = ctk.CTkButton(
            time_frame,
            text="Save Time",
            command=self.save_hard_stop_time,
            width=80
        )
        self.save_time_button.pack(side="left")
        
        # Admin password change
        password_frame = ctk.CTkFrame(main_frame)
        password_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        password_label = ctk.CTkLabel(
            password_frame,
            text="Change Administrator Password",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        password_label.pack(pady=(15, 10))
        
        self.new_password_entry = ctk.CTkEntry(
            password_frame,
            placeholder_text="New password (min 8 characters)",
            show="*"
        )
        self.new_password_entry.pack(fill="x", padx=20, pady=(0, 10))
        
        self.confirm_password_entry = ctk.CTkEntry(
            password_frame,
            placeholder_text="Confirm new password",
            show="*"
        )
        self.confirm_password_entry.pack(fill="x", padx=20, pady=(0, 15))
        
        self.change_password_button = ctk.CTkButton(
            password_frame,
            text="Change Password",
            command=self.change_admin_password
        )
        self.change_password_button.pack(pady=(0, 15))
    
    def create_time_logs_tab(self):
        """Create the time logs viewer interface"""
        # Filters frame
        filters_frame = ctk.CTkFrame(self.logs_tab)
        filters_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        # Employee filter
        emp_filter_frame = ctk.CTkFrame(filters_frame)
        emp_filter_frame.pack(side="left", padx=(10, 5), pady=10)
        
        emp_filter_label = ctk.CTkLabel(emp_filter_frame, text="Employee:")
        emp_filter_label.pack(side="left", padx=(10, 5))
        
        self.employee_filter = ctk.CTkComboBox(
            emp_filter_frame,
            values=["All Employees"],
            command=self.filter_logs
        )
        self.employee_filter.pack(side="left", padx=(0, 10))
        
        # Date filters
        date_frame = ctk.CTkFrame(filters_frame)
        date_frame.pack(side="left", padx=5, pady=10)
        
        start_label = ctk.CTkLabel(date_frame, text="Start Date:")
        start_label.pack(side="left", padx=(10, 5))
        
        # Set default start date to yesterday
        yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        
        self.start_date_entry = ctk.CTkEntry(
            date_frame,
            placeholder_text="YYYY-MM-DD",
            width=100
        )
        self.start_date_entry.pack(side="left", padx=(0, 10))
        self.start_date_entry.insert(0, yesterday)  # Default to yesterday
        
        end_label = ctk.CTkLabel(date_frame, text="End Date:")
        end_label.pack(side="left", padx=(0, 5))
        
        # Set default end date to today
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        
        self.end_date_entry = ctk.CTkEntry(
            date_frame,
            placeholder_text="YYYY-MM-DD",
            width=100
        )
        self.end_date_entry.pack(side="left", padx=(0, 10))
        self.end_date_entry.insert(0, today)  # Default to today
        
        # Status filter
        status_frame = ctk.CTkFrame(filters_frame)
        status_frame.pack(side="left", padx=5, pady=10)
        
        status_label = ctk.CTkLabel(status_frame, text="Status:")
        status_label.pack(side="left", padx=(10, 5))
        
        self.status_filter = ctk.CTkComboBox(
            status_frame,
            values=["All Statuses", "VERIFIED", "UNVERIFIED_OFFLINE", "FLAGGED_SUSPICIOUS", "AUTO_RESOLVED", "MANUAL_CORRECTION"],
            command=self.filter_logs
        )
        self.status_filter.pack(side="left", padx=(0, 10))
        
        # Refresh button
        self.refresh_logs_button = ctk.CTkButton(
            filters_frame,
            text="Refresh",
            command=self.refresh_logs,
            width=80
        )
        self.refresh_logs_button.pack(side="right", padx=10, pady=10)
        
        # Logs display
        logs_frame = ctk.CTkFrame(self.logs_tab)
        logs_frame.pack(fill="both", expand=True, padx=10, pady=(5, 10))
        
        # Logs header
        logs_header = ctk.CTkLabel(
            logs_frame,
            text="Time Logs",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        logs_header.pack(pady=(10, 5))
        
        # Logs list
        self.logs_listbox = ctk.CTkScrollableFrame(logs_frame)
        self.logs_listbox.pack(fill="both", expand=True, padx=10, pady=(0, 10))
    
    def refresh_data(self):
        """Refresh all data displays"""
        self.refresh_employee_list()
        self.load_system_config()
        self.refresh_logs()
    
    def refresh_employee_list(self):
        """Refresh the employee list"""
        # Clear existing list
        for widget in self.employee_listbox.winfo_children():
            widget.destroy()
        
        # Get active employees
        employees = self.db_manager.get_active_employees()
        
        if not employees:
            no_emp_label = ctk.CTkLabel(
                self.employee_listbox,
                text="No active employees found.",
                font=ctk.CTkFont(size=14),
                text_color="gray"
            )
            no_emp_label.pack(pady=20)
            return
        
        # Create employee buttons
        for employee in employees:
            emp_frame = ctk.CTkFrame(self.employee_listbox)
            emp_frame.pack(fill="x", padx=5, pady=2)
            
            pin_indicator = "🔒" if employee['has_pin'] else "🔓"
            emp_button = ctk.CTkButton(
                emp_frame,
                text=f"{pin_indicator} {employee['name']}",
                command=lambda e=employee: self.select_employee(e),
                height=35,
                fg_color="transparent",
                text_color=("gray10", "gray90"),
                hover_color=("gray70", "gray30")
            )
            emp_button.pack(fill="x", padx=5, pady=3)
    
    def select_employee(self, employee: Dict[str, Any]):
        """Select an employee for editing"""
        self.current_employee = employee
        self.name_entry.delete(0, 'end')
        self.name_entry.insert(0, employee['name'])
        self.pin_entry.delete(0, 'end')
        
        # Enable update and remove buttons
        self.update_button.configure(state="normal")
        self.remove_button.configure(state="normal")
        self.add_button.configure(state="disabled")
    
    def clear_employee_form(self):
        """Clear the employee form"""
        self.current_employee = None
        self.name_entry.delete(0, 'end')
        self.pin_entry.delete(0, 'end')
        
        # Reset button states
        self.update_button.configure(state="disabled")
        self.remove_button.configure(state="disabled")
        self.add_button.configure(state="normal")
    
    def add_employee(self):
        """Add a new employee"""
        name = self.name_entry.get().strip()
        pin = self.pin_entry.get().strip()
        
        if not name:
            self.show_message("Please enter an employee name.", "error")
            return
        
        if pin and not self.time_service.validate_pin(pin):
            self.show_message("PIN must be 4 digits and not a common/weak PIN.", "error")
            return
        
        success = self.db_manager.add_employee(name, pin if pin else None)
        if success:
            self.show_message(f"Employee '{name}' added successfully.", "success")
            self.clear_employee_form()
            self.refresh_employee_list()
            if self.refresh_callback:
                self.refresh_callback()
        else:
            self.show_message("Failed to add employee. Name may already exist.", "error")
    
    def update_employee(self):
        """Update the selected employee"""
        if not self.current_employee:
            return
        
        name = self.name_entry.get().strip()
        pin = self.pin_entry.get().strip()
        
        if not name:
            self.show_message("Please enter an employee name.", "error")
            return
        
        if pin and not self.time_service.validate_pin(pin):
            self.show_message("PIN must be 4 digits and not a common/weak PIN.", "error")
            return
        
        success = self.db_manager.update_employee(
            self.current_employee['id'],
            name=name,
            pin=pin if pin else None
        )
        
        if success:
            self.show_message(f"Employee '{name}' updated successfully.", "success")
            self.clear_employee_form()
            self.refresh_employee_list()
            if self.refresh_callback:
                self.refresh_callback()
        else:
            self.show_message("Failed to update employee.", "error")
    
    def remove_employee(self):
        """Remove the selected employee"""
        if not self.current_employee:
            return
        
        # Show confirmation dialog
        confirm_dialog = ctk.CTkToplevel(self)
        confirm_dialog.title("Confirm Removal")
        confirm_dialog.geometry("400x200")
        confirm_dialog.resizable(False, False)
        confirm_dialog.transient(self)
        confirm_dialog.grab_set()

        # Set custom icon
        try:
            confirm_dialog.iconbitmap("appicon.ico")
        except Exception:
            pass  # Fallback to default icon if file not found

        # Center the dialog
        confirm_dialog.update_idletasks()
        x = (confirm_dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (confirm_dialog.winfo_screenheight() // 2) - (200 // 2)
        confirm_dialog.geometry(f"400x200+{x}+{y}")
        
        confirm_label = ctk.CTkLabel(
            confirm_dialog,
            text=f"Remove employee '{self.current_employee['name']}'?\n\nThis will deactivate the employee but preserve their time logs.",
            font=ctk.CTkFont(size=16),
            wraplength=350
        )
        confirm_label.pack(pady=(30, 20))
        
        def confirm_removal():
            success = self.db_manager.deactivate_employee(self.current_employee['id'])
            if success:
                self.show_message(f"Employee '{self.current_employee['name']}' removed successfully.", "success")
                self.clear_employee_form()
                self.refresh_employee_list()
                if self.refresh_callback:
                    self.refresh_callback()
            else:
                self.show_message("Failed to remove employee.", "error")
            confirm_dialog.destroy()
        
        button_frame = ctk.CTkFrame(confirm_dialog)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        cancel_button = ctk.CTkButton(
            button_frame,
            text="Cancel",
            command=confirm_dialog.destroy,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=(0, 10), expand=True)
        
        remove_button = ctk.CTkButton(
            button_frame,
            text="Remove",
            command=confirm_removal,
            fg_color="red"
        )
        remove_button.pack(side="right", expand=True)
    
    def load_system_config(self):
        """Load current system configuration"""
        # Load PIN requirement
        pin_required = self.db_manager.get_config('pin_required') == 'true'
        self.pin_required_var.set(pin_required)
        
        # Load admin password requirement
        admin_password_required = self.db_manager.get_config('admin_password_required') != 'false'
        self.admin_password_required_var.set(admin_password_required)
        
        # Load hard stop time
        hard_stop_time = self.db_manager.get_config('hard_stop_time') or '20:00'
        hour, minute = hard_stop_time.split(':')
        hour = int(hour)
        minute = int(minute)
        
        # Convert 24-hour to 12-hour format
        if hour == 0:
            hour = 12
            ampm = "AM"
        elif hour < 12:
            ampm = "AM"
        elif hour == 12:
            ampm = "PM"
        else:
            hour -= 12
            ampm = "PM"
        
        self.hour_var.set(str(hour))
        self.minute_var.set(f"{minute:02d}")
        self.ampm_var.set(ampm)
    
    def toggle_pin_requirement(self):
        """Toggle PIN requirement setting"""
        pin_required = self.pin_required_var.get()
        self.db_manager.set_config('pin_required', 'true' if pin_required else 'false')
        self.show_message(f"PIN requirement {'enabled' if pin_required else 'disabled'}.", "success")
    
    def toggle_admin_password_requirement(self):
        """Toggle admin password requirement setting"""
        admin_password_required = self.admin_password_required_var.get()
        self.db_manager.set_config('admin_password_required', 'true' if admin_password_required else 'false')
        self.show_message(f"Admin panel password requirement {'enabled' if admin_password_required else 'disabled'}.", "success")
    
    def save_hard_stop_time(self):
        """Save the hard stop time"""
        try:
            hour = int(self.hour_var.get())
            minute = int(self.minute_var.get())
            ampm = self.ampm_var.get()
            
            if not (1 <= hour <= 12 and 0 <= minute <= 59):
                self.show_message("Invalid time format. Use 12-hour format (1-12:00-59).", "error")
                return
            
            # Convert 12-hour to 24-hour format
            if ampm == "PM" and hour != 12:
                hour += 12
            elif ampm == "AM" and hour == 12:
                hour = 0
            
            time_str = f"{hour:02d}:{minute:02d}"
            display_time = f"{self.hour_var.get()}:{minute:02d} {ampm}"
            self.db_manager.set_config('hard_stop_time', time_str)
            self.show_message(f"Hard stop time set to {display_time}.", "success")
            
        except ValueError:
            self.show_message("Please enter valid numbers for hour and minute.", "error")
    
    def change_admin_password(self):
        """Change the administrator password"""
        new_password = self.new_password_entry.get()
        confirm_password = self.confirm_password_entry.get()
        
        if not new_password or len(new_password) < 8:
            self.show_message("Password must be at least 8 characters long.", "error")
            return
        
        if new_password != confirm_password:
            self.show_message("Passwords do not match.", "error")
            return
        
        success = self.db_manager.change_admin_password(new_password)
        if success:
            self.show_message("Administrator password changed successfully.", "success")
            self.new_password_entry.delete(0, 'end')
            self.confirm_password_entry.delete(0, 'end')
        else:
            self.show_message("Failed to change password.", "error")
    
    def refresh_logs(self):
        """Refresh the time logs display"""
        # Clear existing logs
        for widget in self.logs_listbox.winfo_children():
            widget.destroy()
        
        # Get filter values
        employee_filter = self.employee_filter.get()
        start_date = self.start_date_entry.get()
        end_date = self.end_date_entry.get()
        status_filter = self.status_filter.get()
        
        # Get logs
        logs = self.db_manager.get_time_logs(
            start_date=start_date if start_date else None,
            end_date=end_date if end_date else None,
            status=status_filter if status_filter != "All Statuses" else None
        )
        
        if not logs:
            no_logs_label = ctk.CTkLabel(
                self.logs_listbox,
                text="No logs found matching the current filters.",
                font=ctk.CTkFont(size=14),
                text_color="gray"
            )
            no_logs_label.pack(pady=20)
            return
        
        # Group logs by employee and date
        employee_days = {}
        for log in logs:
            # Parse timestamp to get date
            try:
                timestamp = datetime.datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
                date_str = timestamp.strftime('%Y-%m-%d')
                employee_name = log['employee_name']
                
                if employee_name not in employee_days:
                    employee_days[employee_name] = {}
                if date_str not in employee_days[employee_name]:
                    employee_days[employee_name][date_str] = []
                
                employee_days[employee_name][date_str].append({
                    'action': log['action'],
                    'timestamp': timestamp,
                    'status': log['status'],
                    'time_str': timestamp.strftime('%I:%M:%S %p')
                })
            except Exception as e:
                print(f"Error parsing timestamp: {e}")
                continue
        
        # Display logs organized by employee and date
        for employee_name in sorted(employee_days.keys()):
            # Employee header
            emp_header_frame = ctk.CTkFrame(self.logs_listbox)
            emp_header_frame.pack(fill="x", padx=5, pady=(10, 5))
            
            emp_header_label = ctk.CTkLabel(
                emp_header_frame,
                text=f"👤 {employee_name}",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color="blue"
            )
            emp_header_label.pack(anchor="w", padx=10, pady=10)
            
            # Sort dates for this employee
            for date_str in sorted(employee_days[employee_name].keys(), reverse=True):
                # Date header
                date_header_frame = ctk.CTkFrame(self.logs_listbox)
                date_header_frame.pack(fill="x", padx=10, pady=(5, 2))
                
                # Format date for display
                date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')
                date_display = date_obj.strftime('%A, %B %d, %Y')
                
                date_header_label = ctk.CTkLabel(
                    date_header_frame,
                    text=f"📅 {date_display}",
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color="green"
                )
                date_header_label.pack(anchor="w", padx=10, pady=5)
                
                # Timeline for this day
                timeline_frame = ctk.CTkFrame(self.logs_listbox)
                timeline_frame.pack(fill="x", padx=15, pady=(0, 10))
                
                # Sort events by time for this day
                day_events = sorted(employee_days[employee_name][date_str], key=lambda x: x['timestamp'])
                
                for i, event in enumerate(day_events):
                    # Create timeline entry
                    event_frame = ctk.CTkFrame(timeline_frame)
                    event_frame.pack(fill="x", padx=5, pady=2)
                    
                    # Status color coding
                    status_colors = {
                        'VERIFIED': 'green',
                        'UNVERIFIED_OFFLINE': 'orange',
                        'FLAGGED_SUSPICIOUS': 'red',
                        'AUTO_RESOLVED': 'blue',
                        'MANUAL_CORRECTION': 'purple'
                    }
                    status_color = status_colors.get(event['status'], 'gray')
                    
                    # Action icon and text with better icons
                    if event['action'] == 'SIGN_IN':
                        action_icon = "🟢 ➡️"  # Green circle with arrow in
                        action_text = "Sign In"
                        icon_color = "green"
                    else:  # SIGN_OUT
                        action_icon = "🔴 ⬅️"  # Red circle with red arrow out
                        action_text = "Sign Out"
                        icon_color = "red"
                    
                    # Timeline connector
                    if i < len(day_events) - 1:
                        connector = "├─" if i < len(day_events) - 1 else "└─"
                    else:
                        connector = "└─"
                    
                    # Create a frame to hold icon and text separately
                    content_frame = ctk.CTkFrame(event_frame, fg_color="transparent")
                    content_frame.pack(fill="x", padx=10, pady=5)
                    
                    # Icon label with its own color
                    icon_label = ctk.CTkLabel(
                        content_frame,
                        text=f"{connector} {action_icon}",
                        text_color=icon_color,
                        font=ctk.CTkFont(size=12, weight="bold"),
                        anchor="w"
                    )
                    icon_label.pack(side="left")
                    
                    # Text label with status color
                    text_label = ctk.CTkLabel(
                        content_frame,
                        text=f" {action_text} at {event['time_str']} ({event['status']})",
                        text_color=status_color,
                        font=ctk.CTkFont(size=12),
                        anchor="w"
                    )
                    text_label.pack(side="left")
                    
                    # Add timeline line if not the last event
                    if i < len(day_events) - 1:
                        line_frame = ctk.CTkFrame(timeline_frame, height=1)
                        line_frame.pack(fill="x", padx=15, pady=0)
                        line_frame.configure(fg_color="gray")
                
                # Add summary for this day
                if len(day_events) > 1:
                    # Calculate total time worked if we have sign-in and sign-out pairs
                    total_time = self.calculate_total_time_worked(day_events)
                    if total_time:
                        summary_frame = ctk.CTkFrame(timeline_frame)
                        summary_frame.pack(fill="x", padx=5, pady=(5, 0))
                        
                        summary_label = ctk.CTkLabel(
                            summary_frame,
                            text=f"⏱️ Total time worked: {total_time}",
                            font=ctk.CTkFont(size=11, weight="bold"),
                            text_color="darkblue"
                        )
                        summary_label.pack(anchor="w", padx=10, pady=5)
    
    def calculate_total_time_worked(self, day_events):
        """Calculate total time worked for a day's events"""
        total_minutes = 0
        sign_in_time = None
        
        for event in day_events:
            if event['action'] == 'SIGN_IN':
                sign_in_time = event['timestamp']
            elif event['action'] == 'SIGN_OUT' and sign_in_time:
                # Calculate time difference
                time_diff = event['timestamp'] - sign_in_time
                total_minutes += time_diff.total_seconds() / 60
                sign_in_time = None  # Reset for next pair
        
        if total_minutes > 0:
            hours = int(total_minutes // 60)
            minutes = int(total_minutes % 60)
            if hours > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{minutes}m"
        return None
    
    def filter_logs(self, value=None):
        """Filter logs based on current selections"""
        self.refresh_logs()
    
    def show_message(self, message: str, message_type: str = "info"):
        """Show a message to the user"""
        # Create a simple message dialog
        message_dialog = ctk.CTkToplevel(self)
        message_dialog.title("Message")
        message_dialog.geometry("400x150")
        message_dialog.resizable(False, False)
        message_dialog.transient(self)
        message_dialog.grab_set()

        # Set custom icon
        try:
            message_dialog.iconbitmap("appicon.ico")
        except Exception:
            pass  # Fallback to default icon if file not found

        # Center the dialog
        message_dialog.update_idletasks()
        x = (message_dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (message_dialog.winfo_screenheight() // 2) - (150 // 2)
        message_dialog.geometry(f"400x150+{x}+{y}")
        
        # Message color
        colors = {
            "success": "green",
            "error": "red",
            "info": "blue"
        }
        color = colors.get(message_type, "black")
        
        message_label = ctk.CTkLabel(
            message_dialog,
            text=message,
            text_color=color,
            font=ctk.CTkFont(size=14),
            wraplength=350
        )
        message_label.pack(expand=True)
        
        ok_button = ctk.CTkButton(
            message_dialog,
            text="OK",
            command=message_dialog.destroy
        )
        ok_button.pack(pady=(0, 20)) 