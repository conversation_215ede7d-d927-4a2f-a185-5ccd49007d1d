# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 0, 0, 0)
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    # Set mask to place a check mark by "Product Version"
    mask=0x3f,
    # Contains a bitmask that specifies the valid bits 'flags'r
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x40004,
    # The general type of file.
    # 0x1 = application, 0x2 = DLL
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        '040904B0',
        [StringStruct('CompanyName', 'PolicyTime'),
        StringStruct('FileDescription', 'Employee Time Tracking Application'),
        StringStruct('FileVersion', '*******'),
        StringStruct('InternalName', 'PolicyTime'),
        StringStruct('LegalCopyright', 'PolicyTime Application'),
        StringStruct('OriginalFilename', 'PolicyTime.exe'),
        StringStruct('ProductName', 'PolicyTime Employee Tracking'),
        StringStruct('ProductVersion', '1.0.0')])
      ]),
    VarFileInfo([VarStruct('Translation', [1033, 1200])])
  ]
)