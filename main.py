#!/usr/bin/env python3
"""
PolicyTime - Employee Time Tracking Application
Main entry point for the application
"""

import sys
import os
import logging
import customtkinter as ctk
from database import DatabaseManager
from time_service import TimeService
from employee_interface import EmployeeInterface

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('policytime.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def setup_initial_admin(db_manager: DatabaseManager) -> bool:
    """Setup the initial administrator password"""
    # Create setup dialog
    setup_dialog = ctk.CTkToplevel()
    setup_dialog.title("Initial Setup - PolicyTime")
    setup_dialog.geometry("500x400")
    setup_dialog.resizable(False, False)
    setup_dialog.grab_set()

    # Set custom icon
    try:
        setup_dialog.iconbitmap("appicon.ico")
    except Exception:
        pass  # Fallback to default icon if file not found

    # Center the dialog
    setup_dialog.update_idletasks()
    x = (setup_dialog.winfo_screenwidth() // 2) - (500 // 2)
    y = (setup_dialog.winfo_screenheight() // 2) - (400 // 2)
    setup_dialog.geometry(f"500x400+{x}+{y}")
    
    # Main frame
    main_frame = ctk.CTkFrame(setup_dialog)
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # Welcome message
    welcome_label = ctk.CTkLabel(
        main_frame,
        text="Welcome to PolicyTime!",
        font=ctk.CTkFont(size=24, weight="bold")
    )
    welcome_label.pack(pady=(20, 10))
    
    desc_label = ctk.CTkLabel(
        main_frame,
        text="This is the first time running PolicyTime.\nPlease set up the administrator password to continue.",
        font=ctk.CTkFont(size=14),
        wraplength=450
    )
    desc_label.pack(pady=(0, 30))
    
    # Password entry
    password_frame = ctk.CTkFrame(main_frame)
    password_frame.pack(fill="x", padx=20, pady=(0, 20))
    
    password_label = ctk.CTkLabel(
        password_frame,
        text="Administrator Password:",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    password_label.pack(anchor="w", padx=10, pady=(15, 5))
    
    password_entry = ctk.CTkEntry(
        password_frame,
        placeholder_text="Enter password (minimum 8 characters)",
        show="*",
        font=ctk.CTkFont(size=14)
    )
    password_entry.pack(fill="x", padx=10, pady=(0, 10))
    
    confirm_password_entry = ctk.CTkEntry(
        password_frame,
        placeholder_text="Confirm password",
        show="*",
        font=ctk.CTkFont(size=14)
    )
    confirm_password_entry.pack(fill="x", padx=10, pady=(0, 15))
    
    # Error message
    error_label = ctk.CTkLabel(
        main_frame,
        text="",
        text_color="red",
        font=ctk.CTkFont(size=12)
    )
    error_label.pack(pady=(0, 20))
    
    # Setup result
    setup_result = {"success": False}
    
    def validate_and_setup():
        password = password_entry.get()
        confirm_password = confirm_password_entry.get()
        
        if not password or len(password) < 8:
            error_label.configure(text="Password must be at least 8 characters long.")
            return
        
        if password != confirm_password:
            error_label.configure(text="Passwords do not match.")
            return
        
        # Attempt to setup admin
        success = db_manager.setup_initial_admin(password)
        if success:
            setup_result["success"] = True
            setup_dialog.destroy()
        else:
            error_label.configure(text="Failed to setup administrator. Please try again.")
    
    # Buttons
    button_frame = ctk.CTkFrame(main_frame)
    button_frame.pack(fill="x", padx=20, pady=(0, 20))
    
    cancel_button = ctk.CTkButton(
        button_frame,
        text="Cancel",
        command=lambda: setup_dialog.destroy(),
        fg_color="gray"
    )
    cancel_button.pack(side="left", padx=(0, 10), expand=True)
    
    setup_button = ctk.CTkButton(
        button_frame,
        text="Setup Administrator",
        command=validate_and_setup
    )
    setup_button.pack(side="right", expand=True)
    
    # Bind Enter key
    password_entry.bind('<Return>', lambda e: validate_and_setup())
    confirm_password_entry.bind('<Return>', lambda e: validate_and_setup())
    
    # Focus on password entry
    password_entry.focus()
    
    # Wait for dialog to close
    setup_dialog.wait_window()
    
    return setup_result["success"]

def main():
    """Main application entry point"""
    try:
        # Set appearance mode and color theme
        ctk.set_appearance_mode("light")  # Modes: "System" (standard), "Dark", "Light"
        ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"
        
        # Initialize database manager
        db_manager = DatabaseManager()
        
        # Check if admin is already set up
        if not db_manager.admin_exists():
            # Show initial setup dialog
            if not setup_initial_admin(db_manager):
                print("Setup cancelled. Exiting...")
                return
            print("Administrator setup completed successfully.")
        
        # Initialize time service
        time_service = TimeService(db_manager)
        
        # Create and run the main application
        app = EmployeeInterface(db_manager, time_service)
        
        # Center the main window
        app.update_idletasks()
        x = (app.winfo_screenwidth() // 2) - (800 // 2)
        y = (app.winfo_screenheight() // 2) - (600 // 2)
        app.geometry(f"800x600+{x}+{y}")
        
        print("PolicyTime application started successfully.")
        print("Database file: policytime.db")
        print("Log file: policytime.log")
        
        # Start the main loop
        app.mainloop()
        
    except Exception as e:
        logging.error(f"Application error: {e}")
        print(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 